import React, { useEffect, useState } from "react";
import { View } from "@tarojs/components";
import {
  Avatar,
  Button,
  ConfigProvider,
  SafeArea,
} from "@nutui/nutui-react-taro";
import zhCN from "@nutui/nutui-react-taro/dist/locales/zh-CN";
import "./index.scss";
import Taro from "@tarojs/taro";
import { getHomeHabitsOverview } from "src/services/habits";
import Timeline from "../../components/timeline";
import { TimelineHabitItem } from "../../types/timeline";
import { CircleProgress } from "@nutui/nutui-react-taro";
import dayjs from "dayjs";
import { getAllBabies } from "../babies/services";
import { Menu } from "@nutui/nutui-react-taro";
import BoyAvatar from "../../assets/avatar-boy.png";
import GirlAvatar from "../../assets/avatar-girl.png";

function Index() {
  const [locale] = useState(zhCN);
  const [babies, setBabies] = useState<any[]>([]);
  const [habits, setHabits] = useState<TimelineHabitItem[]>([]);
  const defaultBaby = babies.find((b) => b.is_default);

  const [overview, setOverview] = useState({
    today_completed_count: 0,
    today_completion_rate: 0,
    today_total_count: 0,
    today_timeline: [],
  });

  // 示例数据
  const mockHabits: TimelineHabitItem[] = [
    {
      habit_icon: "📚",
      habit_id: "f5e2ba2d-cf2e-425b-adae-67ee893dd4e8",
      habit_name: "阅读",
      is_completed: true,
      preferred_time: "08:00:00",
      theme_color: "#FF8A80",
    },
    {
      habit_icon: "☀️",
      habit_id: "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      habit_name: "户外活动",
      is_completed: false,
      preferred_time: "13:00:00",
      theme_color: "#81C784",
    },
    {
      habit_icon: "🦷",
      habit_id: "b2c3d4e5-f6g7-8901-bcde-f23456789012",
      habit_name: "刷牙",
      is_completed: true,
      preferred_time: "18:00:00",
      theme_color: "#64B5F6",
    },
    {
      habit_icon: "🐰",
      habit_id: "c3d4e5f6-g7h8-9012-cdef-g34567890123",
      habit_name: "自由时间活动",
      is_completed: false,
      preferred_time: null,
      theme_color: "#D4A574",
    },
  ];

  useEffect(() => {
    getAllBabies().then((res: any) => setBabies(res.data));
    getHomeHabitsOverview().then((res: any) => setOverview(res.data));
    // 设置示例数据
    setHabits(mockHabits);
  }, []);

  const handleHabitClick = (habit: TimelineHabitItem) => {
    console.log("点击习惯:", habit);
    Taro.navigateTo({
      url: `/pages/habits/detail/index?id=${habit.habit_id}`,
    });
  };

  const handleToggleComplete = (habitId: string, isCompleted: boolean) => {
    setHabits((prev) =>
      prev.map((habit) =>
        habit.habit_id === habitId
          ? { ...habit, is_completed: isCompleted }
          : habit
      )
    );
  };

  return (
    <View className="home-page">
      
      <View className="overview-info">
        <View className="header-avatar">
          <Avatar
            size="large"
            src={
              defaultBaby?.avatar_url ??
              `${defaultBaby?.gender === 1 ? BoyAvatar : GirlAvatar}`
            }
          />
          {defaultBaby?.nickname}
          <View>{/* {dayjs().format('MM月DD日')} */}</View>
        </View>

        <CircleProgress
          radius="30"
          percent={overview.today_completion_rate}
          strokeWidth={10}
          color="#7FB3D3"
        >
          {Math.round(overview.today_completion_rate * 100)}%
        </CircleProgress>
      </View>

      <View>今日打卡</View>     
      <Timeline
        habits={overview.today_timeline}
        onHabitClick={handleHabitClick}
        onToggleComplete={handleToggleComplete}
      />
    </View>
  );
}

export default Index;
