import React, { useState } from 'react';
import { View } from '@tarojs/components';
import { showToast } from '@tarojs/taro';
import CheckinCalendar from '../../components/checkin';

const CheckinDemo: React.FC = () => {
  const [checkinData, setCheckinData] = useState([
    // 示例数据 - 您提供的数据结构
    {"date":"2025-07-31T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-01T16:00:00.000Z","is_completed":true},
    {"date":"2025-08-02T16:00:00.000Z","is_completed":true},
    {"date":"2025-08-03T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-04T16:00:00.000Z","is_completed":true},
    {"date":"2025-08-05T16:00:00.000Z","is_completed":true},
    {"date":"2025-08-06T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-07T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-08T16:00:00.000Z","is_completed":true},
    {"date":"2025-08-09T16:00:00.000Z","is_completed":true},
    {"date":"2025-08-10T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-11T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-12T16:00:00.000Z","is_completed":true},
    {"date":"2025-08-13T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-14T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-15T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-16T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-17T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-18T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-19T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-20T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-21T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-22T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-23T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-24T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-25T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-26T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-27T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-28T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-29T16:00:00.000Z","is_completed":false},
    {"date":"2025-08-30T16:00:00.000Z","is_completed":false}
  ]);

  // 处理日期点击事件
  const handleDayClick = (date: string, isCompleted: boolean) => {
    const newData = checkinData.map(item => {
      if (item.date === date) {
        return { ...item, is_completed: !item.is_completed };
      }
      return item;
    });

    setCheckinData(newData);

    showToast({
      title: isCompleted ? '取消打卡' : '打卡成功',
      icon: 'success',
      duration: 1500
    });
  };

  return (
    <View className="checkin-demo-page">
      <CheckinCalendar
        data={checkinData}
        title="Check-in History"
        onDayClick={handleDayClick}
        showStats={true}
      />
    </View>
  );
};

export default CheckinDemo;
