import React, { useEffect, useMemo, useState } from 'react'
import { View, ScrollView } from '@tarojs/components'
import { Tabs, Cell, Button } from '@nutui/nutui-react-taro'
import './index.scss'
import { getHabitsLibrary } from 'src/services/habits'
import TabsDemo from 'src/components/tabs/demo'

interface HabitCategory {
  id: string
  name: string
}

interface HabitTemplateItem {
  id: string
  name: string
  description?: string
  categoryId: string
}

function HabitLibrary() {
  const [activeKey, setActiveKey] = useState<string>('all')

  const [habitCategoryList, setHabitCategoryList] = useState<HabitCategory[]>([])

  const filteredHabits = useMemo(() => {
    // if (activeKey === 'all') return mockHabits
    console.log(habitCategoryList, activeKey)
    return habitCategoryList.find((h) => h.id === activeKey)?.children ?? []
  }, [habitCategoryList,activeKey])

  const handleAddHabit = (habit: HabitTemplateItem) => {
    // TODO: 调用接口将模板添加为用户习惯
    console.log('添加习惯模板: ', habit)
  }

  useEffect(()=>{
    getHabitsLibrary().then((res) => {
      console.log(res.data)
      setHabitCategoryList(res.data)
      setActiveKey(res.data[0].id)
    })
  },[])

  return (
    <View className='habit-library-page'>

      <Tabs value={activeKey} onChange={(val) => setActiveKey(String(val))} className='habit-tabs'>
        {habitCategoryList.map((c) => (
          <Tabs.TabPane value={c.id} title={c.name}  />
        ))}
      </Tabs>

      <ScrollView className='habit-list' scrollY>
        <Cell.Group>
          {filteredHabits.map((habit) => (
            <Cell key={habit.id} title={habit.name} description={habit.description} extra={<Button size='small' type='primary' onClick={() => handleAddHabit(habit)}>添加</Button>} />
          ))}
          {filteredHabits.length === 0 && (
            <View className='empty'>暂无习惯</View>
          )}
        </Cell.Group>
      </ScrollView>
    </View>
  )
}

export default HabitLibrary

