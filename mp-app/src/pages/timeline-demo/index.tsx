import React, { useState } from 'react'
import { View, Text } from '@tarojs/components'
import Timeline from '../../components/timeline'
import { TimelineHabitItem } from '../../types/timeline'
import './index.scss'

const TimelineDemo: React.FC = () => {
  const [habits, setHabits] = useState<TimelineHabitItem[]>([
    {
      "habit_id": "bff4349d-459d-4b0c-be69-a43c2fff1f4c",
      "habit_name": "测试",
      "habit_icon": "apple",
      "theme_color": "#7FB3D3",
      "is_completed": false,
      "preferred_time": "06:00:00"
    },
    {
      "habit_id": "f5e2ba2d-cf2e-425b-adae-67ee893dd4e8",
      "habit_name": "刷牙",
      "habit_icon": "rabbit",
      "theme_color": "#D4A574",
      "is_completed": true,
      "preferred_time": "20:00:00"
    },
    {
      "habit_id": "e8831895-b194-4caa-a696-16a1b9fcd959",
      "habit_name": "户外",
      "habit_icon": "ball",
      "theme_color": "#A8D8D8",
      "is_completed": false,
      "preferred_time": null
    },
    {
      "habit_id": "sleep-habit-id",
      "habit_name": "Sleep",
      "habit_icon": "sleep",
      "theme_color": "#7FB3D3",
      "is_completed": false,
      "preferred_time": "07:30:00"
    },
    {
      "habit_id": "feeding-habit-id",
      "habit_name": "Feeding",
      "habit_icon": "feeding",
      "theme_color": "#FFB6C1",
      "is_completed": false,
      "preferred_time": "08:00:00"
    },
    {
      "habit_id": "play-habit-id",
      "habit_name": "Play",
      "habit_icon": "play",
      "theme_color": "#A8D8D8",
      "is_completed": false,
      "preferred_time": "08:30:00"
    },
    {
      "habit_id": "learning-habit-id",
      "habit_name": "Learning",
      "habit_icon": "learning",
      "theme_color": "#F0E68C",
      "is_completed": false,
      "preferred_time": "09:00:00"
    }
  ])

  const handleHabitClick = (habit: TimelineHabitItem) => {
    console.log('Habit clicked:', habit)
  }

  const handleToggleComplete = (habitId: string, isCompleted: boolean) => {
    setHabits(prevHabits =>
      prevHabits.map(habit =>
        habit.habit_id === habitId
          ? { ...habit, is_completed: isCompleted }
          : habit
      )
    )
  }

  return (
    <View className="timeline-demo">
      <View className="demo-header">
        <Text className="demo-title">时间轴组件演示</Text>
        <Text className="demo-subtitle">Timeline Component Demo</Text>
      </View>

      <Timeline
        habits={habits}
        onHabitClick={handleHabitClick}
        onToggleComplete={handleToggleComplete}
      />
    </View>
  )
}

export default TimelineDemo
