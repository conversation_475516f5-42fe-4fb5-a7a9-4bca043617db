# CheckinCalendar 打卡日历组件

一个美观的打卡日历组件，用于显示用户的打卡历史记录。

## 功能特性

- 📅 美观的日历界面设计
- 👣 打卡状态可视化（脚印图标）
- 📊 打卡统计信息显示
- 🎨 渐变背景和现代化UI
- 📱 响应式设计，支持移动端
- 🌙 深色模式支持
- ⚡ 性能优化（使用 useMemo）

## 使用方法

### 基本用法

```tsx
import CheckinCalendar from '@/components/checkin';

const MyPage = () => {
  const checkinData = [
    {"date":"2025-08-01T16:00:00.000Z","is_completed":true},
    {"date":"2025-08-02T16:00:00.000Z","is_completed":false},
    // ... 更多数据
  ];

  return (
    <CheckinCalendar
      data={checkinData}
      title="我的打卡记录"
    />
  );
};
```

### 带交互功能的用法

```tsx
import { useState } from 'react';
import { showToast } from '@tarojs/taro';
import CheckinCalendar from '@/components/checkin';

const MyPage = () => {
  const [checkinData, setCheckinData] = useState([
    {"date":"2025-08-01T16:00:00.000Z","is_completed":true},
    {"date":"2025-08-02T16:00:00.000Z","is_completed":false},
    // ... 更多数据
  ]);

  const handleDayClick = (date: string, isCompleted: boolean) => {
    // 切换打卡状态
    const newData = checkinData.map(item => {
      if (item.date === date) {
        return { ...item, is_completed: !item.is_completed };
      }
      return item;
    });

    setCheckinData(newData);

    showToast({
      title: isCompleted ? '取消打卡' : '打卡成功',
      icon: 'success'
    });
  };

  return (
    <CheckinCalendar
      data={checkinData}
      title="我的打卡记录"
      onDayClick={handleDayClick}
      showStats={true}
    />
  );
};
```

### Props 参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| data | CheckinData[] | 是 | - | 打卡数据数组 |
| title | string | 否 | 'Check-in History' | 日历标题 |
| onDayClick | (date: string, isCompleted: boolean) => void | 否 | - | 日期点击回调函数 |
| showStats | boolean | 否 | true | 是否显示统计信息 |

### CheckinData 数据结构

```typescript
interface CheckinData {
  date: string;        // ISO 8601 格式的日期字符串
  is_completed: boolean; // 是否已完成打卡
}
```

### 示例数据

```javascript
const checkinData = [
  {"date":"2025-07-31T16:00:00.000Z","is_completed":false},
  {"date":"2025-08-01T16:00:00.000Z","is_completed":true},
  {"date":"2025-08-02T16:00:00.000Z","is_completed":true},
  {"date":"2025-08-03T16:00:00.000Z","is_completed":false},
  // ... 更多数据
];
```

## 设计说明

- **已完成打卡**: 显示绿色渐变背景和白色脚印图标
- **未完成打卡**: 显示半透明白色背景和日期数字
- **空白日期**: 不显示任何内容
- **统计信息**: 显示完成天数/总天数和完成百分比

## 样式定制

组件使用 SCSS 编写样式，支持以下定制：

- 修改 `index.scss` 中的颜色变量
- 调整渐变背景色
- 修改圆角大小
- 调整字体大小和间距

## 技术栈

- React + TypeScript
- Taro 框架
- Day.js 日期处理
- SCSS 样式

## 浏览器兼容性

- 支持所有现代浏览器
- 支持微信小程序
- 支持支付宝小程序
- 支持其他小程序平台
