.timeline {
  .timeline-group {
    display: flex;
    margin-bottom: 30px;
    position: relative;

    .timeline-time {
      width: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .time-text {
        font-size: 28rpx;
        font-weight: 500;
        color: #666;
        margin-bottom: 10px;
        background-color: #fff;  
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
      }

      .timeline-line {
        width: 2px;
        height: 100px;
        background-color: #e0e0e0;
        position: absolute;
        top: 40px;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    .timeline-content {
      flex: 1;
      margin-left: 20px;

      .habit-item {
        display: flex;
        align-items: center;
        background-color: white;
        border-radius: 16px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;

        &:active {
          transform: scale(0.98);
        }

        .habit-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background-color: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          .icon-text {
            font-size: 24px;
          }
        }

        .habit-info {
          flex: 1;

          .habit-name {
            font-size: 18px;
            font-weight: 500;
            color: #333;
          }
        }

        .completion-button {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &.completed {
            opacity: 1;
          }

          &.incomplete {
            opacity: 0.7;
          }

          .completion-icon {
            color: white;
            font-size: 20px;
            font-weight: bold;
          }

          &:active {
            transform: scale(0.9);
          }
        }
      }
    }
  }
}