import React from 'react'
import { View, Text } from '@tarojs/components'
import { TimelineProps, TimelineHabitItem, TimelineGroup } from '../../types/timeline'
import './index.scss'

const Timeline: React.FC<TimelineProps> = ({
  habits,
  onHabitClick,
  onToggleComplete
}) => {
  // 将习惯按时间分组
  const groupHabitsByTime = (habits: TimelineHabitItem[]): TimelineGroup[] => {
    const groups: { [key: string]: TimelineHabitItem[] } = {}

    habits.forEach(habit => {
      let time: string
      if (habit.preferred_time === null) {
        time = 'no-time' // 特殊标识符，用于无时间的习惯
      } else {
        time = habit.preferred_time.substring(0, 5) // 提取 HH:mm
      }

      if (!groups[time]) {
        groups[time] = []
      }
      groups[time].push(habit)
    })

    // 按时间排序，将 'no-time' 排在最后
    return Object.entries(groups)
      .map(([time, habits]) => ({
        time: time === 'no-time' ? '待定' : time,
        habits
      }))
      .sort((a, b) => {
        if (a.time === '待定') return 1
        if (b.time === '待定') return -1
        return a.time.localeCompare(b.time)
      })
  }

  const timelineGroups = groupHabitsByTime(habits)

  const handleHabitClick = (habit: TimelineHabitItem) => {
    onHabitClick?.(habit)
  }

  const handleToggleComplete = (habitId: string, currentStatus: boolean) => {
    onToggleComplete?.(habitId, !currentStatus)
  }

  return (
    <View className="timeline">
      {timelineGroups.map((group, groupIndex) => (
        <View key={group.time} className="timeline-group">
          <View className="timeline-time">
            <Text className="time-text">{group.time}</Text>
            {groupIndex < timelineGroups.length - 1 && (
              <View className="timeline-line" />
            )}
          </View>

          <View className="timeline-content">
            {group.habits.map((habit) => (
              <View
                key={habit.habit_id}
                className="habit-item"
                onClick={() => handleHabitClick(habit)}
              >
                <View className="habit-icon">
                  <Text className="icon-text">{habit.habit_icon}</Text>
                </View>

                <View className="habit-info">
                  <Text className="habit-name">{habit.habit_name}</Text>
                </View>

                <View
                  className={`completion-button ${habit.is_completed ? 'completed' : 'incomplete'}`}
                  style={{ backgroundColor: habit.theme_color }}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleToggleComplete(habit.habit_id, habit.is_completed)
                  }}
                >
                  <Text className="completion-icon">
                    {habit.is_completed ? '✓' : '+'}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      ))}
    </View>
  )
}

export default Timeline