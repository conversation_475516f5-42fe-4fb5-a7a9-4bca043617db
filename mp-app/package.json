{"name": "little-steps", "version": "1.0.0", "private": true, "description": "小脚步", "templateInfo": {"name": "react-NutUI", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@nutui/nutui-react-taro": "^2.6.14", "@tarojs/components": "4.1.5", "@tarojs/helper": "4.1.5", "@tarojs/plugin-framework-react": "4.1.5", "@tarojs/plugin-html": "4.1.5", "@tarojs/plugin-platform-alipay": "4.1.5", "@tarojs/plugin-platform-h5": "4.1.5", "@tarojs/plugin-platform-jd": "4.1.5", "@tarojs/plugin-platform-qq": "4.1.5", "@tarojs/plugin-platform-swan": "4.1.5", "@tarojs/plugin-platform-tt": "4.1.5", "@tarojs/plugin-platform-weapp": "4.1.5", "@tarojs/react": "4.1.5", "@tarojs/runtime": "4.1.5", "@tarojs/shared": "4.1.5", "@tarojs/taro": "4.1.5", "dayjs": "^1.11.13", "react": "^18.0.0", "react-dom": "^18.0.0", "zustand": "^5.0.7"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-react": "^7.24.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "4.1.5", "@tarojs/taro-loader": "4.1.5", "@tarojs/webpack5-runner": "4.1.5", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "4.1.5", "eslint": "^8.12.0", "eslint-config-taro": "4.1.5", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "postcss": "^8.4.18", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.1.0", "webpack": "5.78.0"}}